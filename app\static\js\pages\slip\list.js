import { setupInputConverters } from '../../common/utils.js';

// DOMContentLoadedイベントリスナーを削除し、必要な機能のみを残す
export function initializeSlipList() {
    setupInputConverters();  // 共通の入力変換機能を設定
    setupCSVImport();       // CSVインポート機能の設定
    setupWorkTableConversion(); // ワークテーブル変換機能の設定
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const bulkUnapproveBtn = document.getElementById('bulk-unapprove-btn');
    const slipCheckboxes = document.querySelectorAll('.slip-checkbox');
    const masterCheckbox = document.getElementById('master-checkbox');

    // マスターチェックボックスの状態を更新する関数
    function updateMasterCheckboxState() {
        if (!masterCheckbox) return;

        const checkboxes = Array.from(slipCheckboxes);
        const allChecked = checkboxes.length > 0 && checkboxes.every(cb => cb.checked);
        const someChecked = checkboxes.some(cb => cb.checked);

        masterCheckbox.checked = allChecked;
        masterCheckbox.indeterminate = someChecked && !allChecked;
    }

    // マスターチェックボックスのイベントリスナー
    if (masterCheckbox) {
        masterCheckbox.addEventListener('change', function() {
            slipCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkButtons();
        });
    }

    function updateBulkButtons() {
        if (bulkDeleteBtn) {
            const hasPendingSlips = Array.from(slipCheckboxes)
                .some(cb => cb.checked && cb.dataset.status === 'pending' && !cb.closest('tr').classList.contains('slip-sent'));
            bulkDeleteBtn.disabled = !hasPendingSlips;
        }
        
        if (bulkUnapproveBtn) {
            const hasApprovedSlips = Array.from(slipCheckboxes)
                .some(cb => cb.checked && cb.dataset.status === 'approved' && !cb.closest('tr').classList.contains('slip-sent'));
            bulkUnapproveBtn.disabled = !hasApprovedSlips;
        }
    }

    slipCheckboxes.forEach(checkbox => {
        // 送信済み伝票のチェックボックスを無効化
        if (checkbox.closest('tr').classList.contains('slip-sent')) {
            checkbox.disabled = true;
        }
        
        checkbox.addEventListener('change', updateBulkButtons);
    });

    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', async function() {
            if (!confirm('選択した伝票を一括削除してもよろしいですか？\n削除された伝票は復元できません。')) {
                return;
            }

            try {
                // 処理中の表示
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 処理中...';

                // 選択された伝票のIDを取得
                const checkboxes = document.querySelectorAll('input[name="slip_ids"]:checked');
                const slipIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

                if (slipIds.length === 0) {
                    alert('削除する伝票を選択してください');
                    this.disabled = false;
                    this.textContent = '一括削除';
                    return;
                }

                // CSRFトークンを取得
                const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

                const response = await fetch('/bulk-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({ slip_ids: slipIds })
                });

                const data = await response.json();

                if (response.ok) {
                    alert(data.message || '選択した伝票を一括削除しました');
                    window.location.reload();
                } else {
                    console.error('Response status:', response.status);
                    console.error('Response data:', data);
                    alert(data.error || '一括削除処理に失敗しました');
                    // ボタンを元の状態に戻す
                    this.disabled = false;
                    this.textContent = '一括削除';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('一括削除処理中にエラーが発生しました');
                // ボタンを元の状態に戻す
                this.disabled = false;
                this.textContent = '一括削除';
            }
        });
    }

    if (bulkUnapproveBtn) {
        bulkUnapproveBtn.addEventListener('click', async function() {
            if (!confirm('選択した伝票の承認を一括取消してもよろしいですか？')) {
                return;
            }

            try {
                // 処理中の表示
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 処理中...';

                // 選択された伝票のIDを取得
                const checkboxes = document.querySelectorAll('input[name="slip_ids"]:checked');
                const slipIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

                if (slipIds.length === 0) {
                    alert('承認取消する伝票を選択してください');
                    this.disabled = false;
                    this.textContent = '一括承認取消';
                    return;
                }

                // CSRFトークンを取得
                const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

                const response = await fetch('/bulk-unapprove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({ slip_ids: slipIds })
                });

                const data = await response.json();

                if (response.ok) {
                    if (data.has_send_flg) {
                        alert('送信済みの伝票が含まれているため、一括承認取消できません。');
                    } else {
                        alert(data.message || '選択した伝票の承認を一括取消しました');
                    }
                    window.location.reload();
                } else {
                    console.error('Response status:', response.status);
                    console.error('Response data:', data);
                    if (data.has_send_flg) {
                        alert('送信済みの伝票が含まれているため、一括承認取消できません。');
                    } else {
                        alert(data.error || '一括承認取消処理に失敗しました');
                    }
                    // ボタンを元の状態に戻す
                    this.disabled = false;
                    this.textContent = '一括承認取消';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('一括承認取消処理中にエラーが発生しました');
                // ボタンを元の状態に戻す
                this.disabled = false;
                this.textContent = '一括承認取消';
            }
        });
    }

    // 初期状態の設定
    updateMasterCheckboxState();
    updateBulkButtons();
}

// CSVインポート機能の設定
function setupCSVImport() {
    const importButton = document.getElementById('import-csv-btn');
    const fileInput = document.getElementById('csv-file-input');

    if (!importButton || !fileInput) return;

    // インポート処理の状態を確認する関数
    async function checkImportStatus(taskId) {
        const maxAttempts = 300; // 最大試行回数（300回 = 約5分）
        let attempts = 0;
        let progressMessage = false;

        // インポート中のメッセージを表示するための要素を作成
        const progressDiv = document.createElement('div');
        progressDiv.className = 'position-fixed top-0 start-0 w-100 bg-info text-white p-2 text-center';
        progressDiv.style.zIndex = '9999';
        progressDiv.innerHTML = 'CSVインポート処理中です。しばらくお待ちください...';
        progressDiv.style.display = 'none';
        document.body.appendChild(progressDiv);

        // 30秒経過したらプログレスメッセージを表示
        setTimeout(() => {
            if (!progressMessage) {
                progressDiv.style.display = 'block';
                progressMessage = true;
            }
        }, 30000);

        while (attempts < maxAttempts) {
            try {
                const statusResponse = await fetch(`/purchase/slips/import-status/${taskId}`);
                const statusResult = await statusResponse.json();

                if (statusResponse.ok) {
                    if (statusResult.status === 'completed') {
                        // 処理完了
                        if (progressMessage) {
                            progressDiv.remove();
                        }
                        alert(statusResult.message || 'CSVのインポートが完了しました');
                        window.location.reload();
                        return;
                    } else if (statusResult.status === 'failed') {
                        // 処理失敗
                        if (progressMessage) {
                            progressDiv.remove();
                        }
                        alert(statusResult.error || 'CSVのインポートに失敗しました');
                        return;
                    } else if (statusResult.progress) {
                        // 進捗情報がある場合は表示
                        if (!progressMessage) {
                            progressDiv.style.display = 'block';
                            progressMessage = true;
                        }
                        progressDiv.innerHTML = `CSVインポート処理中: ${statusResult.progress}`;
                    }
                    // まだ処理中の場合は待機
                } else {
                    // エラーレスポンス
                    if (progressMessage) {
                        progressDiv.remove();
                    }
                    alert(statusResult.error || 'インポート状態の確認に失敗しました');
                    return;
                }
            } catch (error) {
                console.error('Error checking import status:', error);
                // エラーが発生した場合も続行（一時的なネットワークエラーなどの可能性）
            }

            // 1秒待機してから再試行
            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
        }

        // 最大試行回数を超えた場合
        if (progressMessage) {
            progressDiv.remove();
        }

        // 処理は継続中だが、ユーザーに選択肢を与える
        if (confirm('インポート処理の完了確認がタイムアウトしました。処理は継続中の可能性があります。\n\n処理状況を引き続き確認しますか？')) {
            // 確認を続ける場合は再帰的に呼び出し
            checkImportStatus(taskId);
        } else {
            alert('インポート処理は引き続きバックグラウンドで実行されます。\n画面を更新すると最新の状態が反映されます。');
        }
    }

    importButton.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', async (event) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        // ファイル形式チェック
        for (const file of files) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                alert('CSVファイルのみアップロード可能です。');
                event.target.value = '';
                return;
            }
        }

        const formData = new FormData();
        for (const file of files) {
            formData.append('files[]', file);
        }

        try {
            importButton.disabled = true;
            importButton.innerHTML = '<span class="spinner-border spinner-border-sm"></span> インポート中...';

            // CSRFトークンを取得
            const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

            // インポート処理の開始
            const response = await fetch('/purchase/slips/import-csv', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                body: formData
            });

            const result = await response.json();

            // インポート処理の完了を確認
            if (response.ok) {
                if (result.task_id) {
                    // 非同期処理の場合、完了を確認するためにポーリング
                    await checkImportStatus(result.task_id);
                } else {
                    // 同期処理の場合、そのまま完了メッセージを表示
                    alert(result.message || 'CSVのインポートが完了しました');
                    window.location.reload();
                }
            } else {
                alert(result.error || 'CSVのインポートに失敗しました');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('CSVのインポート中にエラーが発生しました');
        } finally {
            importButton.disabled = false;
            importButton.textContent = 'CSVインポート';
            event.target.value = '';
        }
    });
}

// ワークテーブル変換機能の設定
function setupWorkTableConversion() {
    const convertButton = document.getElementById('convert-work-btn');
    
    if (!convertButton) return;

    convertButton.addEventListener('click', async () => {
        if (!confirm('ワークテーブルから伝票データに変換しますか？\n\n原単価と数量の桁数問題がある伝票については自動補正を試行します。')) {
            return;
        }

        try {
            convertButton.disabled = true;
            convertButton.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 変換中...';

            // CSRFトークンを取得
            const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

            const response = await fetch('/purchase/convert_work_to_slips', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                }
            });

            const result = await response.json();

            if (response.ok) {
                alert(result.message || 'ワークテーブルの変換が完了しました');
                window.location.reload();
            } else {
                alert(result.error || 'ワークテーブルの変換に失敗しました');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('ワークテーブル変換中にエラーが発生しました');
        } finally {
            convertButton.disabled = false;
            convertButton.textContent = 'ワークテーブル変換';
        }
    });
}
