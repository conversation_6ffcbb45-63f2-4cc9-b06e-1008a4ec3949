{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- slip_list.css を読み込む -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/slip_list.css') }}">
<!-- 他のスタイルシートの読み込みを必要に応じて削除 -->
{% endblock %}

{% block scripts %}
{{ super() }}
<script type="module">
    import { initializeSlipList } from "{{ url_for('static', filename='js/pages/slip/list.js') }}";
    
    document.addEventListener('DOMContentLoaded', function() {
        initializeSlipList();
    });
</script>
{% endblock %}

{% block content %}
<div class="slip-list-page">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">検索条件</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('slip_list.list') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">伝票日付範囲</label>
                        <div class="input-group">
                            <input type="date" class="form-control" name="date_from" value="{{ request.args.get('date_from', '') }}">
                            <span class="input-group-text">～</span>
                            <input type="date" class="form-control" name="date_to" value="{{ request.args.get('date_to', '') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">伝票番号</label>
                        <input type="text" class="form-control" name="slip_number" value="{{ request.args.get('slip_number', '') }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">会社コード</label>
                        <input type="text" class="form-control" name="company_code" value="{{ request.args.get('company_code', '') }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">取引先コード</label>
                        <input type="text" class="form-control" name="vendor_code" value="{{ request.args.get('vendor_code', '') }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">課コード</label>
                        <input type="text" class="form-control" name="department_code" value="{{ request.args.get('department_code', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">店舗</label>
                        <select class="form-select" name="store_code">
                            <option value="">店舗を選択</option>
                            {% for store in stores %}
                            <option value="{{ store.store_code }}" {% if store.store_code == request.args.get('store_code') %}selected{% endif %}>
                                {{ store.store_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">作成日</label>
                        <input type="date" class="form-control" name="created_at" value="{{ request.args.get('created_at', '') }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">ステータス</label>
                        <select class="form-select" name="status">
                            <option value="pending" {% if request.args.get('status', 'pending') == 'pending' %}selected{% endif %}>未承認</option>
                            <option value="approved" {% if request.args.get('status') == 'approved' %}selected{% endif %}>承認済</option>
                        </select>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-3">
                        <label class="form-label">金額範囲</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="amount_from" value="{{ request.args.get('amount_from', '') }}" placeholder="下限">
                            <span class="input-group-text">～</span>
                            <input type="number" class="form-control" name="amount_to" value="{{ request.args.get('amount_to', '') }}" placeholder="上限">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">商品コード</label>
                        <input type="text" class="form-control" name="product_code" value="{{ request.args.get('product_code', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">商品名</label>
                        <input type="text" class="form-control" name="product_name" value="{{ request.args.get('product_name', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">ソート順</label>
                        <select class="form-select" name="sort_by">
                            <option value="date_desc" {% if request.args.get('sort_by') == 'date_desc' %}selected{% endif %}>伝票日付（降順）</option>
                            <option value="date_asc" {% if request.args.get('sort_by') == 'date_asc' %}selected{% endif %}>伝票日付（昇順）</option>
                            <option value="cost_amount_desc" {% if request.args.get('sort_by') == 'cost_amount_desc' %}selected{% endif %}>原価金額（降順）</option>
                            <option value="cost_amount_asc" {% if request.args.get('sort_by') == 'cost_amount_asc' %}selected{% endif %}>原価金額（昇順）</option>
                            <option value="selling_amount_desc" {% if request.args.get('sort_by') == 'selling_amount_desc' %}selected{% endif %}>売価金額（降順）</option>
                            <option value="selling_amount_asc" {% if request.args.get('sort_by') == 'selling_amount_asc' %}selected{% endif %}>売価金額（昇順）</option>
                            <option value="vendor_asc" {% if request.args.get('sort_by') == 'vendor_asc' %}selected{% endif %}>取引先名（昇順）</option>
                            <option value="vendor_desc" {% if request.args.get('sort_by') == 'vendor_desc' %}selected{% endif %}>取引先名（降順）</option>
                            <option value="created_at_asc" {% if request.args.get('sort_by') == 'created_at_asc' %}selected{% endif %}>作成日時（昇順）</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">検索</button>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <a href="{{ url_for('slip_list.list') }}" class="btn btn-secondary w-100">クリア</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">仕入伝票一覧</h5>
            <div>
                <input type="file" id="csv-file-input" accept=".csv" style="display: none;" multiple>
                <button id="import-csv-btn" class="btn btn-primary">CSVインポート</button>
                <button id="convert-work-btn" class="btn btn-info ms-2">ワークテーブル変換</button>
                <a href="{{ url_for('slip_list.download_excel', **request.args) }}" class="btn btn-success ms-2">EXCEL</a>
                {% if current_user.is_approver %}
                <button id="bulk-delete-btn" class="btn btn-danger ms-2" disabled>一括削除</button>
                <button id="bulk-unapprove-btn" class="btn btn-warning ms-2" disabled>一括承認取消</button>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="master-checkbox"></th>
                            <th>伝票番号</th>
                            <th>日付</th>
                            <th>会社コード</th>
                            <th>店舗コード</th>
                            <th>店舗名</th>
                            <th>課コード</th>
                            <th>取引先コード</th>
                            <th>取引先名</th>
                            <th>原価金額</th>
                            <th>売価金額</th>
                            <th>作成日時</th>
                            <th>更新日時</th>
                            <th>ステータス</th>
                            <th>送信状態</th>
                            <th>承認者</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for slip in slips %}
                        <tr class="{% if slip.send_flg %}slip-sent{% endif %}">
                            <td><input type="checkbox" class="slip-checkbox" name="slip_ids" value="{{ slip.id }}" data-slip-id="{{ slip.id }}" data-status="{{ slip.status }}"></td>
                            <td>
                                <a href="{{ url_for('slip_detail.detail', slip_id=slip.id) }}">{{ slip.slip_number }}</a>
                            </td>
                            <td>{{ slip.slip_date.strftime('%Y/%m/%d') }}</td>
                            <td>{{ slip.company_code }}</td>
                            <td>{{ slip.store_code }}</td>
                            <td>{{ slip.store_name }}</td>
                            <td>{{ slip.department_code }}</td>
                            <td>{{ slip.vendor_code }}</td>
                            <td>{{ slip.vendor_name }}</td>
                            <td class="text-end">{{ "{:,.0f}".format(slip.cost_amount|float) if slip.cost_amount else '0' }}</td>
                            <td class="text-end">{{ "{:,.0f}".format(slip.selling_amount|float) if slip.selling_amount else '0' }}</td>
                            <td>{% if slip.created_at %}{{ slip.created_at.strftime('%Y/%m/%d %H:%M') }}{% else %}-{% endif %}</td>
                            <td>{% if slip.updated_at %}{{ slip.updated_at.strftime('%Y/%m/%d %H:%M') }}{% else %}-{% endif %}</td>
                            <td>{{ slip.status_display }}</td>
                            <td>
                                {% if slip.send_flg %}
                                    <span class="badge bg-secondary">送信済</span>
                                {% else %}
                                    <span class="badge bg-info">未送信</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if slip.approved_by %}
                                    {{ slip.approver.username }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="pagination-container">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('slip_list.list', page=page-1) }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}
                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('slip_list.list', page=p) }}">{{ p }}</a>
                </li>
                {% endfor %}
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('slip_list.list', page=page+1) }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endblock %}
