from flask import Blueprint, jsonify, request, current_app, send_file, render_template
from flask_login import login_required, current_user
from app import db
from app.models import PurchaseSlip, PurchaseSlipDetail, PurchaseSlipHistory, Store, User
from datetime import datetime
from io import BytesIO
import pandas as pd
from sqlalchemy.orm import joinedload
import json

slip_list_bp = Blueprint('slip_list', __name__)

@slip_list_bp.route('/slip_list')
@login_required
def list():
    page = request.args.get('page', 1, type=int)
    per_page = 20
    query = PurchaseSlip.query\
        .filter(PurchaseSlip.is_deleted == 0)\
        .options(joinedload(PurchaseSlip.details))\
        .outerjoin(User, PurchaseSlip.approved_by == User.id)

    # 会社コードフィルター
    company_code = request.args.get('company_code')
    if company_code:
        query = query.filter(PurchaseSlip.company_code == company_code)

    # 店舗コードフィルター
    store_code = request.args.get('store_code')
    if store_code:
        query = query.filter(PurchaseSlip.store_code == store_code)

    # 日付範囲フィルター
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    if date_from:
        query = query.filter(PurchaseSlip.slip_date >= date_from)
    if date_to:
        query = query.filter(PurchaseSlip.slip_date <= date_to)

    # 金額範囲フィルター
    amount_from = request.args.get('amount_from', type=float)
    amount_to = request.args.get('amount_to', type=float)
    if amount_from is not None:
        query = query.filter(PurchaseSlip.cost_amount >= amount_from)
    if amount_to is not None:
        query = query.filter(PurchaseSlip.cost_amount <= amount_to)

    # その他のフィルター
    slip_number = request.args.get('slip_number')
    if slip_number:
        query = query.filter(PurchaseSlip.slip_number.ilike(f'%{slip_number}%'))

    vendor_code = request.args.get('vendor_code')
    if vendor_code:
        query = query.filter(PurchaseSlip.vendor_code == vendor_code)

    # 課コードフィルター
    department_code = request.args.get('department_code')
    if department_code:
        query = query.filter(PurchaseSlip.department_code == department_code)

    # 商品コードフィルター
    product_code = request.args.get('product_code')
    if product_code:
        query = query.filter(PurchaseSlip.details.any(PurchaseSlipDetail.product_code.ilike(f'%{product_code}%')))

    # 商品名フィルター
    product_name = request.args.get('product_name')
    if product_name:
        query = query.filter(PurchaseSlip.details.any(PurchaseSlipDetail.product_name.ilike(f'%{product_name}%')))

    status = request.args.get('status', 'pending')
    if status:
        query = query.filter(PurchaseSlip.status == status)

    # ソート順
    sort_by = request.args.get('sort_by', 'date_desc')
    if sort_by == 'date_desc':
        query = query.order_by(PurchaseSlip.slip_date.desc())
    elif sort_by == 'date_asc':
        query = query.order_by(PurchaseSlip.slip_date.asc())
    elif sort_by == 'cost_amount_desc':
        query = query.order_by(PurchaseSlip.cost_amount.desc())
    elif sort_by == 'cost_amount_asc':
        query = query.order_by(PurchaseSlip.cost_amount.asc())
    elif sort_by == 'selling_amount_desc':
        query = query.order_by(PurchaseSlip.selling_amount.desc())
    elif sort_by == 'selling_amount_asc':
        query = query.order_by(PurchaseSlip.selling_amount.asc())

    # 作成日フィルター
    created_at = request.args.get('created_at')
    if created_at:
        # created_atの日付部分のみを比較
        query = query.filter(db.func.date(PurchaseSlip.created_at) == created_at)

    slips = query.paginate(page=page, per_page=per_page, error_out=False)
    total_pages = slips.pages
    stores = Store.query.order_by(Store.store_code).all()  # 店舗一覧を取得

    return render_template('slip_list.html', 
        slips=slips.items,
        page=page,
        total_pages=total_pages,
        stores=stores
    )

@slip_list_bp.route('/bulk-delete', methods=['POST'])
@login_required
def bulk_delete_slips():
    if not current_user.is_approver:
        return jsonify({'error': '削除権限がありません'}), 403

    data = request.get_json()
    slip_ids = data.get('slip_ids', [])

    # デバッグログを追加
    current_app.logger.info(f"一括削除処理開始: slip_ids={slip_ids}, user_id={current_user.id}")

    try:
        # slip_idsが空の場合のチェック
        if not slip_ids:
            current_app.logger.warning("slip_idsが空です")
            return jsonify({'error': '対象の伝票が選択されていません'}), 400

        # 受信した伝票IDの詳細をログ出力
        current_app.logger.info(f"受信した伝票ID一覧: {slip_ids}")
        for i, slip_id in enumerate(slip_ids):
            current_app.logger.info(f"  [{i}] slip_id: {slip_id} (type: {type(slip_id)})")

        slips = PurchaseSlip.query.filter(PurchaseSlip.id.in_(slip_ids)).all()
        current_app.logger.info(f"対象伝票数: {len(slips)} / 要求数: {len(slip_ids)}")
        
        # 取得した各伝票の詳細情報をログ出力
        for slip in slips:
            current_app.logger.info(f"  取得伝票: ID={slip.id}, 伝票番号={slip.slip_number}, ステータス={slip.status}, 承認者={slip.approved_by}, 送信フラグ={slip.send_flg}")
        
        # 存在しない伝票IDをチェック
        found_ids = [slip.id for slip in slips]
        missing_ids = [slip_id for slip_id in slip_ids if slip_id not in found_ids]
        if missing_ids:
            current_app.logger.warning(f"存在しない伝票ID: {missing_ids}")
            return jsonify({'error': f'伝票ID {missing_ids} が見つかりません'}), 400
        
        # 対象伝票が見つからない場合
        if not slips:
            current_app.logger.warning("対象の伝票が見つかりません")
            return jsonify({'error': '対象の伝票が見つかりません'}), 400

        # 承認済み伝票のチェック
        approved_slips = [slip for slip in slips if slip.status == 'approved']
        if approved_slips:
            approved_slip_numbers = [slip.slip_number for slip in approved_slips]
            current_app.logger.warning(f"承認済み伝票が含まれています: {approved_slip_numbers}")
            return jsonify({
                'error': f'承認済みの伝票が含まれているため削除できません: {", ".join(approved_slip_numbers)}'
            }), 400

        # 送信済み伝票のチェック
        sent_slips = [slip for slip in slips if slip.send_flg]
        if sent_slips:
            sent_slip_numbers = [slip.slip_number for slip in sent_slips]
            current_app.logger.warning(f"送信済み伝票が含まれています: {sent_slip_numbers}")
            return jsonify({
                'error': f'送信済みの伝票が含まれているため削除できません: {", ".join(sent_slip_numbers)}'
            }), 400

        # 削除処理
        deleted_count = 0
        for slip in slips:
            try:
                # 履歴を記録
                history = PurchaseSlipHistory(
                    slip_id=slip.id,
                    action='delete',
                    user_id=current_user.id,
                    details={'changes': f'一括削除により伝票を削除しました'}
                )
                db.session.add(history)
                
                # 伝票を削除（論理削除）
                slip.is_deleted = True
                deleted_count += 1
                current_app.logger.info(f"伝票削除: ID={slip.id}, 伝票番号={slip.slip_number}")
                
            except Exception as slip_error:
                current_app.logger.error(f"伝票 {slip.slip_number} の削除中にエラー: {str(slip_error)}")
                continue

        db.session.commit()
        current_app.logger.info(f"一括削除完了: {deleted_count}件の伝票を削除")

        return jsonify({
            'status': 'success',
            'message': f'{deleted_count}件の伝票を削除しました',
            'deleted_count': deleted_count
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"一括削除中にエラー: {str(e)}")
        return jsonify({'error': '一括削除処理中にエラーが発生しました'}), 500

@slip_list_bp.route('/bulk-unapprove', methods=['POST'])
@login_required
def bulk_unapprove_slips():
    if not current_user.is_approver:
        return jsonify({'error': '承認取消権限がありません'}), 403

    data = request.get_json()
    slip_ids = data.get('slip_ids', [])

    # デバッグログを追加
    current_app.logger.info(f"一括承認取消処理開始: slip_ids={slip_ids}, user_id={current_user.id}")

    try:
        # slip_idsが空の場合のチェック
        if not slip_ids:
            current_app.logger.warning("slip_idsが空です")
            return jsonify({'error': '対象の伝票が選択されていません'}), 400

        # 受信した伝票IDの詳細をログ出力
        current_app.logger.info(f"受信した伝票ID一覧: {slip_ids}")
        for i, slip_id in enumerate(slip_ids):
            current_app.logger.info(f"  [{i}] slip_id: {slip_id} (type: {type(slip_id)})")

        slips = PurchaseSlip.query.filter(PurchaseSlip.id.in_(slip_ids)).all()
        current_app.logger.info(f"対象伝票数: {len(slips)} / 要求数: {len(slip_ids)}")
        
        # 取得した各伝票の詳細情報をログ出力
        for slip in slips:
            current_app.logger.info(f"  取得伝票: ID={slip.id}, 伝票番号={slip.slip_number}, ステータス={slip.status}, 承認者={slip.approved_by}, 送信フラグ={slip.send_flg}")
        
        # 存在しない伝票IDをチェック
        found_ids = [slip.id for slip in slips]
        missing_ids = [slip_id for slip_id in slip_ids if slip_id not in found_ids]
        if missing_ids:
            current_app.logger.warning(f"存在しない伝票ID: {missing_ids}")
            return jsonify({'error': f'伝票ID {missing_ids} が見つかりません'}), 400
        
        # 対象伝票が見つからない場合
        if not slips:
            current_app.logger.warning("対象の伝票が見つかりません")
            return jsonify({'error': '対象の伝票が見つかりません'}), 400

        has_send_flg = any(slip.send_flg for slip in slips)
        current_app.logger.info(f"送信済み伝票の存在: {has_send_flg}")

        if has_send_flg:
            return jsonify({'has_send_flg': True}), 400

        for slip in slips:
            current_app.logger.info(f"伝票処理開始: slip_id={slip.id}, current_status={slip.status}")
            
            slip.status = 'pending'
            slip.approved_at = None
            slip.approved_by = None
            slip.updated_at = datetime.now()

            # 履歴を記録
            try:
                current_app.logger.info(f"履歴レコード作成開始: slip_id={slip.id}, user_id={current_user.id}")
                
                # 各パラメータの型と値をチェック
                current_app.logger.info(f"slip.id = {slip.id} (type: {type(slip.id)})")
                current_app.logger.info(f"current_user.id = {current_user.id} (type: {type(current_user.id)})")
                current_app.logger.info(f"action = 'unapprove' (type: {type('unapprove')})")
                current_app.logger.info(f"details = '' (type: {type('')})")
                
                # SQLAlchemyレベルでのテスト - 段階的に作成
                current_app.logger.info("Step 1: 必須フィールドのみでオブジェクト作成")
                history = PurchaseSlipHistory()
                current_app.logger.info("Step 1 成功")
                
                current_app.logger.info("Step 2: slip_id設定")
                history.slip_id = int(slip.id)  # 明示的にintに変換
                current_app.logger.info("Step 2 成功")
                
                current_app.logger.info("Step 3: user_id設定")
                history.user_id = int(current_user.id)  # 明示的にintに変換
                current_app.logger.info("Step 3 成功")
                
                current_app.logger.info("Step 4: action設定")
                history.action = 'unapprove'
                current_app.logger.info("Step 4 成功")
                
                current_app.logger.info("Step 5: details設定")
                history.details = ''
                current_app.logger.info("Step 5 成功")
                
                current_app.logger.info("Step 6: session.add()実行")
                db.session.add(history)
                current_app.logger.info("Step 6 成功")
                
            except Exception as history_error:
                current_app.logger.error(f"履歴作成エラー: {str(history_error)}")
                current_app.logger.error(f"履歴作成エラー詳細: {type(history_error).__name__}")
                import traceback
                current_app.logger.error(f"履歴作成スタックトレース: {traceback.format_exc()}")
                
                # SQLAlchemyで失敗した場合、生SQLで試行
                try:
                    current_app.logger.info("生SQLでのINSERT試行開始")
                    sql = """
                    INSERT INTO dbo.purchase_slip_history (slip_id, user_id, action, details, created_at)
                    VALUES (?, ?, ?, ?, GETDATE())
                    """
                    db.session.execute(
                        db.text(sql),
                        {
                            'slip_id': int(slip.id),
                            'user_id': int(current_user.id),
                            'action': 'unapprove',
                            'details': ''
                        }
                    )
                    current_app.logger.info("生SQLでのINSERT成功")
                except Exception as sql_error:
                    current_app.logger.error(f"生SQLでもエラー: {str(sql_error)}")
                    current_app.logger.error(f"生SQLエラー詳細: {type(sql_error).__name__}")
                    raise history_error  # 元のエラーを再発生

        # データベースにコミット
        try:
            db.session.commit()
            current_app.logger.info("データベースコミット成功")
        except Exception as commit_error:
            current_app.logger.error(f"データベースコミットエラー: {str(commit_error)}")
            raise

        return jsonify({'message': '選択した伝票の承認を一括取消しました'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"一括承認取消処理中にエラーが発生: {str(e)}")
        current_app.logger.error(f"エラーの詳細: {type(e).__name__}: {str(e)}")
        import traceback
        current_app.logger.error(f"スタックトレース: {traceback.format_exc()}")
        return jsonify({'error': f'一括承認取消処理中にエラーが発生しました: {str(e)}'}), 500

@slip_list_bp.route('/download_excel', methods=['GET'])
@login_required
def download_excel():
    try:
        query = PurchaseSlip.query\
            .filter(PurchaseSlip.is_deleted == 0)

        # フィルターの適用
        company_code = request.args.get('company_code')
        if company_code:
            query = query.filter(PurchaseSlip.company_code == company_code)

        store_code = request.args.get('store_code')
        if store_code:
            query = query.filter(PurchaseSlip.store_code == store_code)

        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        if date_from:
            query = query.filter(PurchaseSlip.slip_date >= date_from)
        if date_to:
            query = query.filter(PurchaseSlip.slip_date <= date_to)

        amount_from = request.args.get('amount_from', type=float)
        amount_to = request.args.get('amount_to', type=float)
        if amount_from is not None:
            query = query.filter(PurchaseSlip.cost_amount >= amount_from)
        if amount_to is not None:
            query = query.filter(PurchaseSlip.cost_amount <= amount_to)

        slip_number = request.args.get('slip_number')
        if slip_number:
            query = query.filter(PurchaseSlip.slip_number.ilike(f'%{slip_number}%'))

        status = request.args.get('status', 'pending')
        if status:
            query = query.filter(PurchaseSlip.status == status)

        # 作成日フィルター
        created_at = request.args.get('created_at')
        if created_at:
            query = query.filter(db.func.date(PurchaseSlip.created_at) == created_at)

        # データを取得
        slips = query.all()

        # データフレームの作成
        data = [{
            '伝票番号': slip.slip_number,
            '日付': slip.slip_date.strftime('%Y/%m/%d'),
            '会社コード': slip.company_code,
            '店舗コード': slip.store_code,
            '店舗名': slip.store_name,
            '課コード': slip.department_code,
            '取引先コード': slip.vendor_code,
            '取引先名': slip.vendor_name,
            '原価金額': float(slip.cost_amount),
            '売価金額': float(slip.selling_amount),
            'ステータス': '承認済' if slip.status == 'approved' else '未承認'
        } for slip in slips]

        df = pd.DataFrame(data)

        # Excelファイルの作成
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='仕入伝票一覧')

        output.seek(0)

        # 現在の日時を取得してファイル名を作成
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f'仕入伝票一覧_{now}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        current_app.logger.error(f"Excel出力中にエラーが発生: {str(e)}")
        return jsonify({'error': 'Excel出力中にエラーが発生しました。'}), 500


